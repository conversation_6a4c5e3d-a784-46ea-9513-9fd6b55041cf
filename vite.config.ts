import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig, loadEnv } from 'vite';
import viteCompression from 'vite-plugin-compression';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import Components from 'unplugin-vue-components/vite'
import autoIncrementVersion from './autoIncrementVersion'
import { fileURLToPath, URL } from 'node:url';
import { codeInspectorPlugin } from 'code-inspector-plugin';
import postCssPxToRem from 'postcss-pxtorem';

const pathResolve = (dir: string) => {
	return resolve(__dirname, '.', dir);
};

const alias: Record<string, string> = {
	'/@': pathResolve('./src/'),
	'@': fileURLToPath(new URL('./src', import.meta.url)),
	'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
};

export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd());
	return {
		plugins: [
			vue(),
			viteCompression(),
			createSvgIconsPlugin({
				iconDirs: [resolve(process.cwd(), "./src/assets/icons/svg")],
				symbolId: "icon-[name]",
			}),
			Components({
				// 指定组件位置，默认为 src/components
				dirs: ['src/components'],
				// 组件的有效文件扩展名
				extensions: ['vue'],
				// 配置文件生成位置
				dts: 'src/components.d.ts',
				// 自定义组件解析器
				resolvers: [
					// 自定义解析器，确保 SvgSymbol 组件被正确识别
					(componentName) => {
						if (componentName === 'SvgSymbol') {
							return { name: 'default', from: '@/components/SvgSymbol/index.vue' }
						}
					}
				],
				// 包含的组件名称模式
				include: [/\.vue$/, /\.vue\?vue/],
				// 排除的组件
				exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/, /[\\/]\.nuxt[\\/]/],
			}),
			autoIncrementVersion(),
			codeInspectorPlugin({
				bundler: 'vite',
			}),
		],
		root: process.cwd(),
		resolve: { 
			alias,
			preserveSymlinks: true
		},
		base: mode === 'serve' ? './' : env.VITE_PUBLIC_PATH,
		optimizeDeps: { 
			exclude: ['vue-demi'],
		},
		server: {
			host: '0.0.0.0',
			port: Number(env.VITE_PORT) || 3000,
			open: JSON.parse(env.VITE_OPEN || 'false'),
			hmr: {
				overlay: false,
			},
			ws: true,
			proxy: {
				'/vtl': {
					target: `https://**************:8001/vtl/v1`,
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/vtl/, '/'),
				},
				'/login': {
					target: `http://**************:8088`,
					// target: `http://************:8088`,
					// target: 'http://***********:8088', // sjb
					// target: `http://**************:8088`, // yjy
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/login/, '/'),
				},
				'/theapi': {
					// target: `http://**************:8006`,
					// target: `http://***********:8006`, // sjb
					target: `http://**************:8006`, // yjy
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/theapi/, '/'),
				},
				'/user': {
					target: `http://**************:8089`,
					// target: `http://**************:8089`, // yjy
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/user/, '/'),
				},
				'/acapi': {
					// target: `http://**************:9998`,
					target: `http://***********:9998`, // sjb
					// target: `http://192.168.7.93:9998`,
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/acapi/, '/'),
				},
				'/upload': {
					// target: `http://**************:8090`,
					target: `http://**************:8090`, // yjy
					// target: `http://***********:8090`, // sjb
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/upload/, '/'),
				},
				'/glclog': {
					target: `http://192.168.7.177:9996`,
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/glclog/, '/'),
				},
			},
		},
		build: {
			outDir: 'dist',
			chunkSizeWarningLimit: 1500,
			rollupOptions: {
				output: {
					chunkFileNames: 'assets/js/[name]-[hash].js',
					entryFileNames: 'assets/js/[name]-[hash].js',
					assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return id.toString().match(/\/node_modules\/(?!.pnpm)(?<moduleName>[^\/]*)\//)?.groups!.moduleName ?? 'vender';
						}
						if (id.includes('monaco-editor')) {
							return 'monaco-editor';
						}
					},
				},
			},
		},
		css: {
			preprocessorOptions: {
				css: { charset: false },
			},
			postcss: {
				plugins: [
					// 自适应rem布局
					postCssPxToRem({
						// 1rem的大小
						rootValue: 1920 / 10, // 设计稿 / 10
						propList: ['*'],
					}),
				],
			},
		},
		define: {
			__VUE_I18N_LEGACY_API__: JSON.stringify(false),
			__VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
			__INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
			__NEXT_VERSION__: JSON.stringify(process.env.npm_package_version),
			__NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
			global: 'globalThis',
		},
	};
});
