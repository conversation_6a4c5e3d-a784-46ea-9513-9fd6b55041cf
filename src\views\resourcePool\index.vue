<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="pool-btn" v-show="powerItem.sousuo">
							<el-input placeholder="搜索节点" v-model="state.searchValue" @input="onSearch"></el-input>
							<el-button type="primary" plain @click="treeQuery('')" :icon="RefreshRight"></el-button>
						</div>
						<div class="tree-area" v-show="powerItem.liebiao">
							<el-tree
								ref="treeRef"
								:data="treeData"
								node-key="id"
								:props="defaultProps"
								:expand-on-click-node='false'
								default-expand-all
								highlight-current
								:filter-node-method="filterNode"
								@node-click="onNodeClick"
							>
								<template #default="{ node, data }">
									<div class="custom-tree-node">
										<span class="node-label" :title="node.label">{{ node.label }}</span>
										<div class="node-actions">
											<el-link @click.stop="handleAdd(data)" class="action-btn" v-if="addJudge(data)">
												<el-icon><Plus /></el-icon>
											</el-link>
											<el-link type="danger" @click.stop="handleDelete(data)" class="action-btn" v-if="deletJudge(data)">
												<el-icon><Delete /></el-icon>
											</el-link>
										</div>
									</div>
								</template>
							</el-tree>
						</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<!-- 资源节点 -->
					<ResourceNode v-if="state.treeItem.type == 'ziyuan'" :treeItem="state.treeItem" @returnOK="returnOK"></ResourceNode>
					<!-- 主机池 -->
					<PoolNode v-if="state.treeItem.type == 'pool'" :treeItem="state.treeItem" @returnOK="returnOK"></PoolNode>
					<!-- 集群 -->
					<ColonyNode v-if="state.treeItem.type == 'cluster'" :treeItem="state.treeItem" @returnOK="returnOK"></ColonyNode>
					<!-- 主机 -->
					<HostNode v-if="state.treeItem.type == 'host'" :treeItem="state.treeItem" @returnOK="returnOK"></HostNode>
					<!-- 虚拟机 -->
					<VmNode v-if="state.treeItem.type == 'domain'" :treeItem="state.treeItem" @returnOK="returnOK"></VmNode>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref } from 'vue';
import { Search,RefreshRight,Plus,Delete } from '@element-plus/icons-vue';
import { dayjs,FilterNodeMethodFunction,TreeInstance  } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
import { powerCodeQuery } from '/@/api/System'; // 权限
import { convertFlatToTree } from '/@/utils/treeUtils'; // 树形数据转换工具
// 引入组件
const ResourceNode = defineAsyncComponent(() => import('./resourceNode/index.vue'));
const PoolNode = defineAsyncComponent(() => import('./poolNode/index.vue'));
const ColonyNode = defineAsyncComponent(() => import('./colonyNode/index.vue'));
const HostNode = defineAsyncComponent(() => import('./hostNode/index.vue'));
const VmNode = defineAsyncComponent(() => import('./vmNode/index.vue'));

const treeRef = ref<TreeInstance>()
interface Tree {
  [key: string]: any
}
const treeData = ref<Tree[]>([]);
// 配置选项
const defaultProps = {
  children: 'children',
  label: 'name',
}
const state = reactive({
	treeItem: {type:''},
	searchValue: '',
	lastSelectedNodeId: '1', // 保存上次选中的节点ID
});
// 隐藏节点
const filterNode: FilterNodeMethodFunction = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';
// 点击节点事件处理
const onNodeClick = (data: any, node: any) => {
	// 更新当前选中的节点
	state.treeItem = {
		id: data.id,
		level: node.level.toString(),
		name: data.name,
		type: data.type,
		...data
	};
	state.lastSelectedNodeId = data.id;
};
// 设置当前选中的节点
const setCurrentNode = (nodeKey: string) => {
	treeRef.value?.setCurrentKey(nodeKey);
};

// 设置当前选中的节点并更新状态
const setCurrentNodeAndUpdateState = (nodeId: string) => {
	// 设置树组件的选中状态
	setCurrentNode(nodeId);

	// 查找对应的节点数据并更新state
	const findNodeById = (nodes: any[], id: string): any => {
		for (const node of nodes) {
			if (node.id === id) {
				return node;
			}
			if (node.children && node.children.length > 0) {
				const found = findNodeById(node.children, id);
				if (found) return found;
			}
		}
		return null;
	};

	const foundNode = findNodeById(treeData.value, nodeId);
	if (foundNode) {
		state.treeItem = {
			id: foundNode.id,
			level: '0', // 这里可以根据实际层级计算
			name: foundNode.name,
			type: foundNode.type,
			...foundNode
		};
		state.lastSelectedNodeId = nodeId;
		console.log('节点状态已更新:', state.treeItem);
	}
};
const treeQuery = (item: string) => {
  state.searchValue = ''
	let targetNodeId = '1';
	if (item !== 'delete' && state.lastSelectedNodeId) {
		targetNodeId = state.lastSelectedNodeId;
	}
	if (!true) {
		let treeList = [
			{ id: '1', name: '资源节点', pid: '0' },
			{ id: '2', name: '主机池1', pid: '1' },
			{ id: '3', name: '集群1', pid: '2' },
			{ id: '4', name: '主机1', pid: '3' },
			{ id: '5', name: '主机2', pid: '3' },
			{ id: '6', name: '主机3', pid: '3' },
			{ id: '7', name: 'vm1', pid: '4' },
			{ id: '8', name: 'vm2', pid: '4' },
			{ id: '9', name: 'vm3', pid: '5' },
			{ id: '10', name: 'vm4', pid: '5' },
			{ id: '10', name: 'vm5', pid: '5' },
			{ id: '10', name: 'vm3', pid: '5' },
		];
		treeData.value = convertFlatToTree(treeList, {
			idKey: 'id',
			pidKey: 'pid',
			childrenKey: 'children',
			rootValue: '0'
		});
		nextTick(() => {			
			setCurrentNodeAndUpdateState(targetNodeId);
		});
	} else {
		resourceTreeQuery()
			.then((res) => {
				treeData.value = convertFlatToTree(res.data, {
					idKey: 'id',
					pidKey: 'pid',
					childrenKey: 'children',
					rootValue: '0'
				});
				// 数据加载完成后设置选中的节点
				nextTick(() => {
					setCurrentNodeAndUpdateState(targetNodeId);
				});
			})
			.catch((error) => {console.error('加载树形数据失败:', error);});
	}
};
// 检查文本是否溢出的工具函数
const isTextOverflow = (text: string) => {
	// 简单的长度检查，可以根据实际需要调整
	return text && text.length > 15;
};
// 添加判定
const addJudge = (data: any) => {
	console.log('添加判定:', data);
	return true;
};
// 处理添加操作
const handleAdd = (data: any) => {
	console.log('添加节点:', data);
	// 这里添加您的添加逻辑
};
// 删除判定
const deletJudge = (data: any) => {
	console.log('删除判定:', data);
	return true;
};
// 处理删除操作
const handleDelete = (data: any) => {
	console.log('删除节点:', data);
	// 这里添加您的删除逻辑
};
const onSearch = () => {
	treeRef.value!.filter(state.searchValue)
};
const returnOK = (item: string) => {
	treeQuery(item);
};
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
});
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据
const powerQuery = (() => {
	powerCodeQuery({module_code:[
	'jiedianliebiao','jiediansousuo',
	'tianjiazhujichi','xiugaizhujichi','shanchuzhujichi','tianjiajiqun','xiugaijiqun','shanchujiqun',
	'jiqunHA','jiqunDRS','tianjiawuliji','xiugaiwuliji','shanchuwuliji','guanbiwuliji','chongqiwuliji','tuichuwuliji',
	'weihumoshi','tianjiaxuniji','xunijikaiji','xunijiguanji','xunijichongqi','xunijishanchu','xunijiqiangzhichongqi',
	'xunijiguanbidianyuan','xunijizanting','xunijihuifu','xunijiwanquankelong','xunijilianjiekelong','xunijiqianyi',
	'xunijikongzhitai','xunijixiugai','ziyuanjiediangaiyao','zhujichigaiyao','jiqungaiyao','zhujigaiyao','xunijigaiyao','zhujichi',
	'jiqun','wuliji','xuniji','cunchuchi','fenbushijiaohuanji','bendijiaohuanji','yingjianshebei','moban','huishouzhan',
	'xunijibeifen','xunijikuaizhao','yunxingrizhi','qianyirizhi','xingnengjiankong','gaojing','renwu'
	]}).then((res:any)=>{
		powerItem.liebiao = res.data.jiedianliebiao;
		powerItem.sousuo = res.data.jiediansousuo;

		permisData.tianjiazhujichi = res.data.tianjiazhujichi;
		permisData.xiugaizhujichi = res.data.xiugaizhujichi;
		permisData.shanchuzhujichi = res.data.shanchuzhujichi;
		permisData.tianjiajiqun = res.data.tianjiajiqun;
		permisData.xiugaijiqun = res.data.xiugaijiqun;
		permisData.shanchujiqun = res.data.shanchujiqun;
		permisData.jiqunHA = res.data.jiqunHA;
		permisData.jiqunDRS = res.data.jiqunDRS;
		permisData.tianjiawuliji = res.data.tianjiawuliji;
		permisData.xiugaiwuliji = res.data.xiugaiwuliji;
		permisData.shanchuwuliji = res.data.shanchuwuliji;
		permisData.guanbiwuliji = res.data.guanbiwuliji;
		permisData.chongqiwuliji = res.data.chongqiwuliji;
		permisData.tuichuwuliji = res.data.tuichuwuliji;
		permisData.weihumoshi = res.data.weihumoshi;
		permisData.tianjiaxuniji = res.data.tianjiaxuniji;
		permisData.xunijikaiji = res.data.xunijikaiji;
		permisData.xunijiguanji = res.data.xunijiguanji;
		permisData.xunijichongqi = res.data.xunijichongqi;
		permisData.xunijishanchu = res.data.xunijishanchu;
		permisData.xunijiqiangzhichongqi = res.data.xunijiqiangzhichongqi;
		permisData.xunijiguanbidianyuan = res.data.xunijiguanbidianyuan;
		permisData.xunijizanting = res.data.xunijizanting;
		permisData.xunijihuifu = res.data.xunijihuifu;
		permisData.xunijiwanquankelong = res.data.xunijiwanquankelong;
		permisData.xunijilianjiekelong = res.data.xunijilianjiekelong;
		permisData.xunijiqianyi = res.data.xunijiqianyi;
		permisData.xunijikongzhitai = res.data.xunijikongzhitai;
		permisData.xunijixiugai = res.data.xunijixiugai;
		permisData.ziyuanjiediangaiyao = res.data.ziyuanjiediangaiyao;
		permisData.zhujichigaiyao = res.data.zhujichigaiyao;
		permisData.jiqungaiyao = res.data.jiqungaiyao;
		permisData.zhujigaiyao = res.data.zhujigaiyao;
		permisData.xunijigaiyao = res.data.xunijigaiyao;

		permisData.zhujichi = res.data.zhujichi;
		permisData.jiqun = res.data.jiqun;
		permisData.wuliji = res.data.wuliji;
		permisData.xuniji = res.data.xuniji;
		permisData.cunchuchi = res.data.cunchuchi;
		permisData.fenbushijiaohuanji = res.data.fenbushijiaohuanji;
		permisData.bendijiaohuanji = res.data.bendijiaohuanji;
		permisData.yingjianshebei = res.data.yingjianshebei;
		permisData.moban = res.data.moban;
		permisData.huishouzhan = res.data.huishouzhan;
		permisData.xunijibeifen = res.data.xunijibeifen;
		permisData.xunijikuaizhao = res.data.xunijikuaizhao;
		permisData.yunxingrizhi = res.data.yunxingrizhi;
		permisData.qianyirizhi = res.data.qianyirizhi;
		permisData.xingnengjiankong = res.data.xingnengjiankong;
		permisData.gaojing = res.data.gaojing;
		permisData.renwu = res.data.renwu;
		if(powerItem.liebiao) {
			treeQuery('')
		}
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
	// setTimeout(() => {
		
	// }, 500);
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 225px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-btn {
				display: flex;
				justify-content: space-evenly;
				margin-bottom: 15px;
			}
			.tree-area {
				width: 100%;
				height: calc(100% - 40px);
				overflow: auto;
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 245px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
// 树节点焦点样式（如果需要可以取消注释）
// :deep(.el-tree-node:focus > .el-tree-node__content) {
//     background-color: #fff;
//     color: #333;
// }

// 自定义树节点样式
.custom-tree-node {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding-right: 8px;

	// 节点文本部分
	.node-label {
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-right: 8px;
		min-width: 0; // 重要：允许 flex 项目收缩到内容宽度以下
		cursor: pointer;
	}

	// 操作按钮容器
	.node-actions {
		display: flex;
		align-items: center;
		gap: 4px;
		flex-shrink: 0; // 防止按钮被压缩
		opacity: 0.7;
		transition: opacity 0.2s ease;

		.action-btn {
			padding: 4px;
			border-radius: 4px;
			transition: all 0.2s ease;

			&:hover {
				background-color: var(--el-color-primary-light-9);
			}

			.el-icon {
				font-size: 14px;
			}
		}
	}

	// 悬停效果
	&:hover {
		.node-actions {
			opacity: 1;
		}
	}
}

// 针对 Element Plus 树组件的深度样式调整
:deep(.el-tree-node__content) {
	// 确保内容区域有足够的空间
	padding-right: 0 !important;

	// 自定义节点内容的样式
	.custom-tree-node {
		width: 100%;
		min-width: 0;
	}
}
</style>
