<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="pool-btn" v-show="powerItem.sousuo">
							<el-input placeholder="搜索节点" v-model="state.searchValue" @input="onSearch"></el-input>
							<el-button type="primary" plain @click="treeData" :icon="RefreshRight"></el-button>
						</div>
						<div class="tree-area" v-show="powerItem.liebiao">
							<ul id="treeDemo" class="ztree"></ul>
							<!-- <div id="rMenu">
                <ul class="mouse_right_box">
                  <li v-for="(item,index) in mouseData" :key='index' @click="mouseItem(item)">{{item}}</li>
                </ul>
              </div> -->
						</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<!-- 资源节点 -->
					<ResourceNode v-if="state.treeItem.level == '0'" :treeItem="state.treeItem" @returnOK="returnOK"></ResourceNode>
					<!-- 主机池 -->
					<PoolNode v-if="state.treeItem.level == '1'" :treeItem="state.treeItem" @returnOK="returnOK"></PoolNode>
					<!-- 集群 -->
					<ColonyNode v-if="state.treeItem.level == '2'" :treeItem="state.treeItem" @returnOK="returnOK"></ColonyNode>
					<!-- 主机 -->
					<HostNode v-if="state.treeItem.level == '3'" :treeItem="state.treeItem" @returnOK="returnOK"></HostNode>
					<!-- 虚拟机 -->
					<VmNode v-if="state.treeItem.level == '4'" :treeItem="state.treeItem" @returnOK="returnOK"></VmNode>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref } from 'vue';
import { Search,RefreshRight } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
import { powerCodeQuery } from '/@/api/System'; // 权限

// 引入组件
const ResourceNode = defineAsyncComponent(() => import('./resourceNode/index.vue'));
const PoolNode = defineAsyncComponent(() => import('./poolNode/index.vue'));
const ColonyNode = defineAsyncComponent(() => import('./colonyNode/index.vue'));
const HostNode = defineAsyncComponent(() => import('./hostNode/index.vue'));
const VmNode = defineAsyncComponent(() => import('./vmNode/index.vue'));


const searchRef = ref();
const state = reactive({
	treeItem: {
		id: '1',
		level: '0',
	},
	searchValue: '',
});
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';
// 定义图标
const customizeIcon = (treeId: string, treeNode:{tId: string,level:number} ) => {
  let $icon = $("#" + treeNode.tId + "_ico");
  const icons = [ic0, ic1, ic2, ic3, ic4];
  const icon = icons[treeNode.level]; // 根据 level 获取对应图标
  if (icon) {
    $icon.css({
      background: `url(${icon}) no-repeat center center`,
      backgroundSize: '100% 100%',
    });
  }
};
// 点击节点
const onNodeClick = (event: any, treeId: string, treeNode: any) => {
	state.treeItem = treeNode;
};
// 右键点击
const onRightClick = () => {};
// 拖拽
const onDrop = () => {};

const setting = {
	data: {
		simpleData: {
			enable: true,
			idKey: 'id',
			pIdKey: 'pid',
			rootPId: 0,
		},
	},
	view: {
		showIcon: false, // 显示图标
		addDiyDom: customizeIcon, // 自定义图标
		// expandSpeed: "fast",
    fontCss: (treeId:any, treeNode:any) => (treeNode.highlight ? { color: 'red', 'font-weight': 'bold' } : { color: '#333' }),
	},
	callback: {
		onClick: onNodeClick,
		onRightClick: onRightClick,
		onDrop: onDrop,
	},
};
interface Node {
	id: string;
	name: string;
	pid: string;
}
let zNodes: Node[] = [];
const treeData = (item: string) => {
  state.searchValue = ''
	if (!true) {
		zNodes = [
			{ id: '1', name: '资源节点', pid: '0' },
			{ id: '2', name: '主机池1', pid: '1' },
			{ id: '3', name: '集群1', pid: '2' },
			{ id: '4', name: '主机1', pid: '3' },
			{ id: '5', name: '主机2', pid: '3' },
			{ id: '6', name: '主机3', pid: '3' },
			{ id: '7', name: 'vm1', pid: '4' },
			{ id: '8', name: 'vm2', pid: '4' },
			{ id: '9', name: 'vm3', pid: '5' },
			{ id: '10', name: 'vm4', pid: '5' },
			{ id: '10', name: 'vm5', pid: '5' },
			{ id: '10', name: 'vm3', pid: '5' },
		];
		init(item);
	} else {
		resourceTreeQuery()
			.then((res) => {
				zNodes = res.data;
				init(item);
			})
			.catch((error) => {});
	}
};
const init = (item: string) => {
	let zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
	zTreeObj.expandAll(true); // 默认展开所有树的分支
	let treeNode = zTreeObj.getNodeByParam('id', state.treeItem.id);
	if (item == 'delete') {
		treeNode = zTreeObj.getNodeByParam('id', zNodes[0].id);
	} else {
		treeNode = zTreeObj.getNodeByParam('id', state.treeItem.id);
	}
	// 高亮选中根节点
	zTreeObj.selectNode(treeNode);
	state.treeItem = treeNode;
};
const returnOK = (item: string) => {
	treeData(item);
};
const onSearch = () => {
	let zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
  if (!zTreeObj) return;
	const value = state.searchValue.trim();
	const allNodes = zTreeObj.transformToArray(zTreeObj.getNodes());

	// 清除所有节点的高亮状态
	allNodes.forEach((node:any) => {
		node.highlight = false;
		zTreeObj.updateNode(node);
	});

	// 如果搜索值为空，直接返回
	if (!value) return;

	// 获取匹配的节点
	const matchedNodes = zTreeObj.getNodesByParamFuzzy('name', value);

	// 高亮匹配的节点并展开父节点
	matchedNodes.forEach((node:any) => {
		node.highlight = true;
		zTreeObj.updateNode(node);
		zTreeObj.expandNode(node.getParentNode(), true);
	});
};
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
});
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据
const powerQuery = (() => {
	powerCodeQuery({module_code:[
	'jiedianliebiao','jiediansousuo',
	'tianjiazhujichi','xiugaizhujichi','shanchuzhujichi','tianjiajiqun','xiugaijiqun','shanchujiqun',
	'jiqunHA','jiqunDRS','tianjiawuliji','xiugaiwuliji','shanchuwuliji','guanbiwuliji','chongqiwuliji','tuichuwuliji',
	'weihumoshi','tianjiaxuniji','xunijikaiji','xunijiguanji','xunijichongqi','xunijishanchu','xunijiqiangzhichongqi',
	'xunijiguanbidianyuan','xunijizanting','xunijihuifu','xunijiwanquankelong','xunijilianjiekelong','xunijiqianyi',
	'xunijikongzhitai','xunijixiugai','ziyuanjiediangaiyao','zhujichigaiyao','jiqungaiyao','zhujigaiyao','xunijigaiyao','zhujichi',
	'jiqun','wuliji','xuniji','cunchuchi','fenbushijiaohuanji','bendijiaohuanji','yingjianshebei','moban','huishouzhan',
	'xunijibeifen','xunijikuaizhao','yunxingrizhi','qianyirizhi','xingnengjiankong','gaojing','renwu'
	]}).then((res:any)=>{
		powerItem.liebiao = res.data.jiedianliebiao;
		powerItem.sousuo = res.data.jiediansousuo;

		permisData.tianjiazhujichi = res.data.tianjiazhujichi;
		permisData.xiugaizhujichi = res.data.xiugaizhujichi;
		permisData.shanchuzhujichi = res.data.shanchuzhujichi;
		permisData.tianjiajiqun = res.data.tianjiajiqun;
		permisData.xiugaijiqun = res.data.xiugaijiqun;
		permisData.shanchujiqun = res.data.shanchujiqun;
		permisData.jiqunHA = res.data.jiqunHA;
		permisData.jiqunDRS = res.data.jiqunDRS;
		permisData.tianjiawuliji = res.data.tianjiawuliji;
		permisData.xiugaiwuliji = res.data.xiugaiwuliji;
		permisData.shanchuwuliji = res.data.shanchuwuliji;
		permisData.guanbiwuliji = res.data.guanbiwuliji;
		permisData.chongqiwuliji = res.data.chongqiwuliji;
		permisData.tuichuwuliji = res.data.tuichuwuliji;
		permisData.weihumoshi = res.data.weihumoshi;
		permisData.tianjiaxuniji = res.data.tianjiaxuniji;
		permisData.xunijikaiji = res.data.xunijikaiji;
		permisData.xunijiguanji = res.data.xunijiguanji;
		permisData.xunijichongqi = res.data.xunijichongqi;
		permisData.xunijishanchu = res.data.xunijishanchu;
		permisData.xunijiqiangzhichongqi = res.data.xunijiqiangzhichongqi;
		permisData.xunijiguanbidianyuan = res.data.xunijiguanbidianyuan;
		permisData.xunijizanting = res.data.xunijizanting;
		permisData.xunijihuifu = res.data.xunijihuifu;
		permisData.xunijiwanquankelong = res.data.xunijiwanquankelong;
		permisData.xunijilianjiekelong = res.data.xunijilianjiekelong;
		permisData.xunijiqianyi = res.data.xunijiqianyi;
		permisData.xunijikongzhitai = res.data.xunijikongzhitai;
		permisData.xunijixiugai = res.data.xunijixiugai;
		permisData.ziyuanjiediangaiyao = res.data.ziyuanjiediangaiyao;
		permisData.zhujichigaiyao = res.data.zhujichigaiyao;
		permisData.jiqungaiyao = res.data.jiqungaiyao;
		permisData.zhujigaiyao = res.data.zhujigaiyao;
		permisData.xunijigaiyao = res.data.xunijigaiyao;

		permisData.zhujichi = res.data.zhujichi;
		permisData.jiqun = res.data.jiqun;
		permisData.wuliji = res.data.wuliji;
		permisData.xuniji = res.data.xuniji;
		permisData.cunchuchi = res.data.cunchuchi;
		permisData.fenbushijiaohuanji = res.data.fenbushijiaohuanji;
		permisData.bendijiaohuanji = res.data.bendijiaohuanji;
		permisData.yingjianshebei = res.data.yingjianshebei;
		permisData.moban = res.data.moban;
		permisData.huishouzhan = res.data.huishouzhan;
		permisData.xunijibeifen = res.data.xunijibeifen;
		permisData.xunijikuaizhao = res.data.xunijikuaizhao;
		permisData.yunxingrizhi = res.data.yunxingrizhi;
		permisData.qianyirizhi = res.data.qianyirizhi;
		permisData.xingnengjiankong = res.data.xingnengjiankong;
		permisData.gaojing = res.data.gaojing;
		permisData.renwu = res.data.renwu;
		if(powerItem.liebiao) {
			treeData('')
		}
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
	// setTimeout(() => {
		
	// }, 500);
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 225px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-btn {
				display: flex;
				justify-content: space-evenly;
				margin-bottom: 15px;
			}
			.tree-area {
				width: 100%;
				height: calc(100% - 40px);
				overflow: auto;
				#rMenu {
					position: absolute;
					visibility: hidden;
					background-color: var(--el-fill-color-blank);
					z-index: 1;
					border-radius: 4px;
					box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
					.mouse_right_box {
						padding: 5px 0;
						li {
							margin: 0;
							line-height: normal;
							padding: 7px 16px;
							clear: both;
							color: #515a6e;
							cursor: pointer;
							font-size: 14px !important;
						}
						li:hover {
							background-color: #f5f5f5;
						}
					}
				}
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 245px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
