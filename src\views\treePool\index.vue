<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="pool-btn" v-show="powerItem.sousuo">
							<el-input placeholder="搜索节点" v-model="state.searchValue" @input="onSearch"></el-input>
							<el-button type="primary" plain @click="treeQuery('')" :icon="RefreshRight">刷新</el-button>
						</div>
						<div class="tree-area" v-show="powerItem.liebiao">
							<el-tree
								ref="treeRef"
								:data="treeData"
								node-key="id"
								:props="defaultProps"
								:expand-on-click-node='false'
								default-expand-all
								highlight-current
								:filter-node-method="filterNode"
								@node-click="onNodeClick"
							>
							<!-- <template #default="{ node, data }">
								<div class="custom-tree-node">
									<span>{{ node.label }}</span>
									<div>
										<el-link ><el-icon><Plus /></el-icon></el-link>
										<el-link type="danger"><el-icon><Delete /></el-icon></el-link>
										
									</div>
								</div>
							</template> -->
						</el-tree>
						</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<span @click="returnOK('delete')">delete</span>
					<span @click="returnOK('')">刷新</span>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref } from 'vue';
import { Plus,Delete } from '@element-plus/icons-vue';
import { dayjs,FilterNodeMethodFunction,TreeInstance  } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
import { powerCodeQuery } from '/@/api/System'; // 权限
import { convertFlatToTree } from '/@/utils/treeUtils'; // 树形数据转换工具

const treeRef = ref<TreeInstance>()
interface Tree {
  [key: string]: any
}
const treeData = ref<Tree[]>([]);
// 配置选项
const defaultProps = {
  children: 'children',
  label: 'name',
}
// 隐藏节点
const filterNode: FilterNodeMethodFunction = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}
const state = reactive({
	treeItem: {},
	searchValue: '',
	lastSelectedNodeId: '1', // 保存上次选中的节点ID
});
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';

// 点击节点事件处理
const onNodeClick = (data: any, node: any) => {
	// 更新当前选中的节点
	state.treeItem = {
		id: data.id,
		level: node.level.toString(),
		name: data.name,
		type: data.type,
		...data
	};
	state.lastSelectedNodeId = data.id;
};
// 设置当前选中的节点
const setCurrentNode = (nodeKey: string) => {
	treeRef.value?.setCurrentKey(nodeKey);
};

// 设置当前选中的节点并更新状态
const setCurrentNodeAndUpdateState = (nodeId: string) => {
	// 设置树组件的选中状态
	setCurrentNode(nodeId);

	// 查找对应的节点数据并更新state
	const findNodeById = (nodes: any[], id: string): any => {
		for (const node of nodes) {
			if (node.id === id) {
				return node;
			}
			if (node.children && node.children.length > 0) {
				const found = findNodeById(node.children, id);
				if (found) return found;
			}
		}
		return null;
	};

	const foundNode = findNodeById(treeData.value, nodeId);
	if (foundNode) {
		state.treeItem = {
			id: foundNode.id,
			level: '0', // 这里可以根据实际层级计算
			name: foundNode.name,
			type: foundNode.type,
			...foundNode
		};
		state.lastSelectedNodeId = nodeId;
		console.log('节点状态已更新:', state.treeItem);
	}
};
const treeQuery = (item: string) => {
  state.searchValue = ''
	let targetNodeId = '1';
	if (item !== 'delete' && state.lastSelectedNodeId) {
		targetNodeId = state.lastSelectedNodeId;
	}
	const useMockData = true;
	if (useMockData) {
		let treeList = [
			{ id: '1', name: '资源节点', pid: '0',type:'ziyuan' },
			{ id: '2', name: '主机池1', pid: '1',type:'pool' },
			{ id: '3', name: '集群1', pid: '2',type:'cluster' },
			{ id: '4', name: '主机1', pid: '3',type:'host' },
			{ id: '5', name: '主机2', pid: '3', ip: '***********',type:'host' },
			{ id: '7', name: 'vm1安防设施的方法烦烦烦烦烦烦烦烦烦烦烦烦', pid: '4',type:'domain' },
			{ id: '8', name: 'vm2', pid: '4',type:'domain' },
		];
		treeData.value = convertFlatToTree(treeList, {
			idKey: 'id',
			pidKey: 'pid',
			childrenKey: 'children',
			rootValue: '0'
		});
		nextTick(() => {
			setCurrentNodeAndUpdateState(targetNodeId);
		});
	} else {
		resourceTreeQuery()
			.then((res) => {
				// 如果后端返回的也是扁平数据，同样可以使用这个方法转换
				treeData.value = convertFlatToTree(res.data, {
					idKey: 'id',
					pidKey: 'pid',
					childrenKey: 'children',
					rootValue: '0'
				});

				// 数据加载完成后设置选中的节点
				nextTick(() => {
					setCurrentNodeAndUpdateState(targetNodeId);
				});
			})
			.catch((error) => {
				console.error('加载树形数据失败:', error);
			});
	}
};
const returnOK = (item: string) => {
	if (item == 'delete') {
		// 删除操作：重新加载数据并选中根节点
		treeQuery('delete');
	} else {
		// 普通刷新：重新加载数据但保持上次选中的节点
		treeQuery('');
	}
};
const onSearch = () => {
	treeRef.value!.filter(state.searchValue)
};
// 定义变量内容
const powerItem = reactive({
	liebiao: true,
	sousuo: true,
});

// 页面加载时
onMounted(() => {
	// 直接加载树形数据
	treeQuery('');
	// 如果需要权限控制，可以取消注释下面的代码
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 225px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-btn {
				display: flex;
				justify-content: space-evenly;
				margin-bottom: 10px;
			}
			.tree-controls {
				display: flex;
				justify-content: space-around;
				margin-bottom: 10px;
				gap: 5px;
			}
			.tree-area {
				width: 100%;
				height: calc(100% - 40px);
				overflow: auto;
				
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 245px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
:deep(.el-tree-node:focus > .el-tree-node__content) {
    // background-color: #fff;
		// color: #333;
  }
</style>
