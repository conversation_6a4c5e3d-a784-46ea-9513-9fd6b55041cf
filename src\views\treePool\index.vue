<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="pool-btn" v-show="powerItem.sousuo">
							<el-input placeholder="搜索节点" v-model="state.searchValue" @input="onSearch"></el-input>
							<el-button type="primary" plain @click="treeQuery('')" :icon="RefreshRight">刷新</el-button>
						</div>
						<div class="tree-area" v-show="powerItem.liebiao">
							<el-tree
								ref="treeRef"
								style="max-width: 300px"
								:data="treeData"
								node-key="id"
								:props="defaultProps"
								:expand-on-click-node='false'
								default-expand-all
								highlight-current
								:filter-node-method="filterNode"
								@node-click="onNodeClick"
							/>
						</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<span @click="returnOK('delete')">delete</span>
					<span @click="returnOK('')">刷新</span>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref } from 'vue';
import { Search,RefreshRight } from '@element-plus/icons-vue';
import { dayjs,FilterNodeMethodFunction,TreeInstance  } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
import { powerCodeQuery } from '/@/api/System'; // 权限
import { convertFlatToTree } from '/@/utils/treeUtils'; // 树形数据转换工具

const treeRef = ref<TreeInstance>()
interface Tree {
  [key: string]: any
}
const treeData = ref<Tree[]>([]);
// 配置选项
const defaultProps = {
  children: 'children',
  label: 'name',
}
// 隐藏节点
const filterNode: FilterNodeMethodFunction = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}
const state = reactive({
	treeItem: {
		id: '1',
		level: '0',
	},
	searchValue: '',
});
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';

// 点击节点事件处理
const onNodeClick = (data: any, node: any, component: any) => {
	console.log('节点被点击:', data);
	console.log('节点信息:', node);

	// 更新当前选中的节点
	state.treeItem = {
		id: data.id,
		level: node.level.toString(),
		name: data.name,
		type: data.type,
		...data
	};

	console.log('当前选中节点:', state.treeItem);
};
// 设置当前选中的节点
const setCurrentNode = (nodeKey: string) => {
	treeRef.value?.setCurrentKey(nodeKey);
};
const treeQuery = (item: string) => {
  state.searchValue = ''
	// 使用模拟数据进行演示，实际使用时可以改为 false 来使用真实接口
	const useMockData = true;
	if (useMockData) {
		let treeList = [
			{ id: '1', name: '资源节点', pid: '0',type:'ziyuan' },
			{ id: '2', name: '主机池1', pid: '1',type:'pool' },
			{ id: '3', name: '集群1', pid: '2',type:'cluster' },
			{ id: '4', name: '主机1', pid: '3',type:'host' },
			{ id: '5', name: '主机2', pid: '3', ip: '***********',type:'host' },
			{ id: '7', name: 'vm1安防设施的方法烦烦烦烦烦烦烦烦烦烦烦烦', pid: '4',type:'domain' },
			{ id: '8', name: 'vm2', pid: '4',type:'domain' },
		];

		console.log('原始扁平数据:', treeList);
		// 使用封装的方法将扁平数据转换为树形结构
		treeData.value = convertFlatToTree(treeList, {
			idKey: 'id',
			pidKey: 'pid',
			childrenKey: 'children',
			rootValue: '0'
		});
		console.log('转换后的树形数据:', treeData.value);
		console.log('treeData 长度:', treeData.value.length);
	} else {
		resourceTreeQuery()
			.then((res) => {
				// 如果后端返回的也是扁平数据，同样可以使用这个方法转换
				treeData.value = convertFlatToTree(res.data, {
					idKey: 'id',
					pidKey: 'pid',
					childrenKey: 'children',
					rootValue: '0'
				});
			})
			.catch((error) => {});
	}
};
const returnOK = (item: string) => {
	if (item == 'delete') {
		treeQuery('delete');
		// 根节点选中
	}else {
		// 刷新但选中的节点不变
		treeQuery('');

	}
	
};
const onSearch = () => {
	treeRef.value!.filter(state.searchValue)
};
// 定义变量内容
const powerItem = reactive({
	liebiao: true,
	sousuo: true,
});
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据
const powerQuery = (() => {
	powerCodeQuery({module_code:[
	'jiedianliebiao','jiediansousuo',
	'tianjiazhujichi','xiugaizhujichi','shanchuzhujichi','tianjiajiqun','xiugaijiqun','shanchujiqun',
	'jiqunHA','jiqunDRS','tianjiawuliji','xiugaiwuliji','shanchuwuliji','guanbiwuliji','chongqiwuliji','tuichuwuliji',
	'weihumoshi','tianjiaxuniji','xunijikaiji','xunijiguanji','xunijichongqi','xunijishanchu','xunijiqiangzhichongqi',
	'xunijiguanbidianyuan','xunijizanting','xunijihuifu','xunijiwanquankelong','xunijilianjiekelong','xunijiqianyi',
	'xunijikongzhitai','xunijixiugai','ziyuanjiediangaiyao','zhujichigaiyao','jiqungaiyao','zhujigaiyao','xunijigaiyao','zhujichi',
	'jiqun','wuliji','xuniji','cunchuchi','fenbushijiaohuanji','bendijiaohuanji','yingjianshebei','moban','huishouzhan',
	'xunijibeifen','xunijikuaizhao','yunxingrizhi','qianyirizhi','xingnengjiankong','gaojing','renwu'
	]}).then((res:any)=>{
		powerItem.liebiao = res.data.jiedianliebiao;
		powerItem.sousuo = res.data.jiediansousuo;

		permisData.tianjiazhujichi = res.data.tianjiazhujichi;
		permisData.xiugaizhujichi = res.data.xiugaizhujichi;
		permisData.shanchuzhujichi = res.data.shanchuzhujichi;
		permisData.tianjiajiqun = res.data.tianjiajiqun;
		permisData.xiugaijiqun = res.data.xiugaijiqun;
		permisData.shanchujiqun = res.data.shanchujiqun;
		permisData.jiqunHA = res.data.jiqunHA;
		permisData.jiqunDRS = res.data.jiqunDRS;
		permisData.tianjiawuliji = res.data.tianjiawuliji;
		permisData.xiugaiwuliji = res.data.xiugaiwuliji;
		permisData.shanchuwuliji = res.data.shanchuwuliji;
		permisData.guanbiwuliji = res.data.guanbiwuliji;
		permisData.chongqiwuliji = res.data.chongqiwuliji;
		permisData.tuichuwuliji = res.data.tuichuwuliji;
		permisData.weihumoshi = res.data.weihumoshi;
		permisData.tianjiaxuniji = res.data.tianjiaxuniji;
		permisData.xunijikaiji = res.data.xunijikaiji;
		permisData.xunijiguanji = res.data.xunijiguanji;
		permisData.xunijichongqi = res.data.xunijichongqi;
		permisData.xunijishanchu = res.data.xunijishanchu;
		permisData.xunijiqiangzhichongqi = res.data.xunijiqiangzhichongqi;
		permisData.xunijiguanbidianyuan = res.data.xunijiguanbidianyuan;
		permisData.xunijizanting = res.data.xunijizanting;
		permisData.xunijihuifu = res.data.xunijihuifu;
		permisData.xunijiwanquankelong = res.data.xunijiwanquankelong;
		permisData.xunijilianjiekelong = res.data.xunijilianjiekelong;
		permisData.xunijiqianyi = res.data.xunijiqianyi;
		permisData.xunijikongzhitai = res.data.xunijikongzhitai;
		permisData.xunijixiugai = res.data.xunijixiugai;
		permisData.ziyuanjiediangaiyao = res.data.ziyuanjiediangaiyao;
		permisData.zhujichigaiyao = res.data.zhujichigaiyao;
		permisData.jiqungaiyao = res.data.jiqungaiyao;
		permisData.zhujigaiyao = res.data.zhujigaiyao;
		permisData.xunijigaiyao = res.data.xunijigaiyao;

		permisData.zhujichi = res.data.zhujichi;
		permisData.jiqun = res.data.jiqun;
		permisData.wuliji = res.data.wuliji;
		permisData.xuniji = res.data.xuniji;
		permisData.cunchuchi = res.data.cunchuchi;
		permisData.fenbushijiaohuanji = res.data.fenbushijiaohuanji;
		permisData.bendijiaohuanji = res.data.bendijiaohuanji;
		permisData.yingjianshebei = res.data.yingjianshebei;
		permisData.moban = res.data.moban;
		permisData.huishouzhan = res.data.huishouzhan;
		permisData.xunijibeifen = res.data.xunijibeifen;
		permisData.xunijikuaizhao = res.data.xunijikuaizhao;
		permisData.yunxingrizhi = res.data.yunxingrizhi;
		permisData.qianyirizhi = res.data.qianyirizhi;
		permisData.xingnengjiankong = res.data.xingnengjiankong;
		permisData.gaojing = res.data.gaojing;
		permisData.renwu = res.data.renwu;
		if(powerItem.liebiao) {
			treeQuery('')
		}
	});
});
// 页面加载时
onMounted(() => {
	// 直接加载树形数据
	treeQuery('');
	// 如果需要权限控制，可以取消注释下面的代码
	// powerQuery()
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 225px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-btn {
				display: flex;
				justify-content: space-evenly;
				margin-bottom: 10px;
			}
			.tree-controls {
				display: flex;
				justify-content: space-around;
				margin-bottom: 10px;
				gap: 5px;
			}
			.tree-area {
				width: 100%;
				height: calc(100% - 40px);
				overflow: auto;
				
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 245px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
