<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="pool-btn" v-show="powerItem.sousuo">
							<el-input
								placeholder="搜索节点"
								v-model="state.searchValue"
								@input="onSearch"
								@change="onSearchChange"
							></el-input>
							<el-button type="primary" plain @click="treeQuery('')" :icon="RefreshRight">刷新</el-button>
						</div>
						<div class="tree-area" v-show="powerItem.liebiao">
							<TreePublick :search="state.searchValue" :datas="state.treeData" :type="state.type" />
						</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<span @click="returnOK('delete')">delete</span>
					<span @click="returnOK('')">刷新</span>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search,RefreshRight } from '@element-plus/icons-vue';
import { dayjs,FilterNodeMethodFunction,TreeInstance  } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
import { convertFlatToTree } from '/@/utils/treeUtils'; // 树形数据转换工具

const TreePublick = defineAsyncComponent(() => import('/@/layout/component/TreePublick.vue'));

const treeRef = ref()
const state = reactive({
	searchValue: '',
	treeData: [],
	type: ''
});
const treeQuery = (item: string) => {
	// 使用模拟数据进行演示，实际使用时可以改为 false 来使用真实接口
	const useMockData = true;
	if (useMockData) {
		let treeList = [
			{ id: '1', name: '资源节点', pid: '0',type:'ziyuan' },
			{ id: '2', name: '主机池1', pid: '1',type:'pool' },
			{ id: '3', name: '集群1', pid: '2',type:'cluster' },
			{ id: '4', name: '主机1', pid: '3',type:'host' },
			{ id: '5', name: '主机2', pid: '3', ip: '***********',type:'host' },
			{ id: '7', name: 'vm1安防设施的方法烦烦烦烦烦烦烦烦烦烦烦烦', pid: '4',type:'domain' },
			{ id: '8', name: 'vm2', pid: '4',type:'domain' },
		];
		// 使用封装的方法将扁平数据转换为树形结构
		let list = convertFlatToTree(treeList, {
			idKey: 'id',
			pidKey: 'pid',
			childrenKey: 'children',
			rootValue: '0'
		});
		console.log('list', state.treeData);
		state.treeData = list;
		console.log('list111', state.treeData);
	} else {
		resourceTreeQuery()
			.then((res) => {
				// 如果后端返回的也是扁平数据，同样可以使用这个方法转换
				let list = convertFlatToTree(res.data, {
					idKey: 'id',
					pidKey: 'pid',
					childrenKey: 'children',
					rootValue: '0'
				});
				// 等待组件加载完成后再调用方法
				nextTick(() => {
					if (treeRef.value) {
						treeRef.value.openDialog(list, state.searchValue, item);
					}
				});
			})
			.catch((error) => {
				console.error('加载树形数据失败:', error);
			});
	}
};
const returnOK = (item: string) => {
	console.log('aaaaa',item);
	state.type = item;
	if (item == 'delete') {
		treeQuery('delete');
	} else {
		treeQuery('');
	}
};
// 定义变量内容
const powerItem = reactive({
	liebiao: true,
	sousuo: true,
});

// 页面加载时
onMounted(() => {
	treeQuery('');
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 225px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-btn {
				display: flex;
				justify-content: space-evenly;
				margin-bottom: 10px;
			}
			.tree-controls {
				display: flex;
				justify-content: space-around;
				margin-bottom: 10px;
				gap: 5px;
			}
			.tree-area {
				width: 100%;
				height: calc(100% - 40px);
				overflow: auto;
				
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 245px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
