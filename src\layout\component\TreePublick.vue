<template>
  <div class="tree-pubick">
    <el-tree
			ref="treeRef"
			:data="treeData"
			node-key="id"
			:props="defaultProps"
			:expand-on-click-node='false'
			default-expand-all
			highlight-current
			:filter-node-method="filterNode"
			@node-click="onNodeClick"
		/>
  </div>
</template>
<script setup lang="ts"> 
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
import { dayjs,FilterNodeMethodFunction,TreeInstance  } from 'element-plus';
const props = defineProps({
	type: {
		type: String,
		required: true,
	},
	datas: {
		type: Array,
		required: true,
	},
	search: {
		type: String,
		required: true,
	},
});
const treeRef = ref<TreeInstance>()
const state = reactive({
	treeItem: {},
	lastSelectedNodeId: '1', // 保存上次选中的节点ID
});
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';
interface Tree {
  [key: string]: any
}
const treeData = ref<Tree[]>([]);
// 配置选项
const defaultProps = {
  children: 'children',
  label: 'name',
}
// 隐藏节点
const filterNode: FilterNodeMethodFunction = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}
const emit = defineEmits(['treeOK']);
// 点击节点事件处理
const onNodeClick = (data: any, node: any) => {
	// 更新当前选中的节点
	state.treeItem = {
		id: data.id,
		level: node.level.toString(),
		name: data.name,
		type: data.type,
		...data
	};
	// 保存当前选中的节点ID
	state.lastSelectedNodeId = data.id;
  emit('treeOK', state.treeItem);
};
// 设置当前选中的节点
const setCurrentNode = (nodeKey: string) => {
	treeRef.value?.setCurrentKey(nodeKey);
};
// 设置当前选中的节点并更新状态
const setCurrentNodeAndUpdateState = (nodeId: string) => {
	// 设置树组件的选中状态
	setCurrentNode(nodeId);

	// 查找对应的节点数据并更新state
	const findNodeById = (nodes: any[], id: string): any => {
		for (const node of nodes) {
			if (node.id === id) {
				return node;
			}
			if (node.children && node.children.length > 0) {
				const found = findNodeById(node.children, id);
				if (found) return found;
			}
		}
		return null;
	};

	const foundNode = findNodeById(treeData.value, nodeId);
	if (foundNode) {
		state.treeItem = {
			id: foundNode.id,
			level: '0', // 这里可以根据实际层级计算
			name: foundNode.name,
			type: foundNode.type,
			...foundNode
		};
		state.lastSelectedNodeId = nodeId;
		console.log('节点状态已更新:', state.treeItem);
	} else {
		console.warn('未找到指定的节点:', nodeId);
	}
};
// 搜索节点
// const onSearch = () => {
// 	treeRef.value!.filter(state.searchValue)
// };

watch(
	() => props.type,
	(val) => {
		console.log('type', val);
		if(val=='delete') {
			// setCurrentNodeAndUpdateState(props.datas.id);
		} else {
			// setCurrentNodeAndUpdateState(state.lastSelectedNodeId);
		}
	}
);
watch(
	() => props.datas,
	(val) => {
		console.log('datas', val);
	}
);
watch(
	() => props.search,
	(val) => {
		console.log('子组件接收', val);
	}
);
</script>
<style lang="less" scoped>
  .tree-pubick {
    width: 100%;
    height: 100%;
  }
</style>