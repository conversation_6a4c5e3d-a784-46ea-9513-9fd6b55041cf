<template>
<div class="tabs-item-area">
  <div class="tabs-item-btn">
    <el-button type="primary" @click="addVMClick" :icon="CirclePlus" v-if="permisData.tianjiaxuniji">添加虚拟机</el-button>
    <!-- <el-button type="info" @click="editClick" :icon="Edit">修改主机</el-button>
    <el-button type="primary" @click="deleteClick" :icon="Delete">删除主机</el-button>
    <el-button type="info" :icon="MostlyCloudy">同步物理机</el-button>
    <el-button type="primary" :icon="Fold" >退出维护模式</el-button>
    <el-button type="info" :icon="SwitchButton" >关闭物理机</el-button>
    <el-button type="primary" :icon="Refresh" >重启物理机</el-button> -->
    <el-dropdown class="dropdown-bth" trigger="click" @command="handleCommand" v-if="permisData.xiugaiwuliji || permisData.shanchuwuliji || permisData.guanbiwuliji || permisData.chongqiwuliji || permisData.weihumoshi">
      <el-button type="info">
        <!-- <el-icon><MoreFilled /></el-icon><el-icon class="el-icon--right"><ArrowDown /></el-icon> -->
        <!-- 更多<el-icon><MoreFilled /></el-icon> -->
        更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="xg" v-if="permisData.xiugaiwuliji">修改主机</el-dropdown-item>
          <el-dropdown-item command="gb" disabled v-if="permisData.guanbiwuliji">关闭主机</el-dropdown-item>
          <el-dropdown-item command="cq" disabled v-if="permisData.chongqiwuliji">重启主机</el-dropdown-item>
          <el-dropdown-item command="wh" disabled v-if="permisData.weihumoshi">维护模式</el-dropdown-item>
          <el-dropdown-item command="sc" style="color:red" divided v-if="permisData.shanchuwuliji">删除主机</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
	<div class="route-list">
		<div v-for="item in state.routeList" v-show="item.code" :class="{ 'route-item': true, 'is-active': isActive(item.name) }" :key="item.name" @click="tagClick(item.name)">
			<span><span>{{ item.name }}</span></span>
		</div>
	</div>
	<div class="tabs-item-center">
    <HostSummary v-if="state.acive == '概要'" :treeItem="props.treeItem" :acive="state.acive"></HostSummary>
    <VM v-if="state.acive == '虚拟机'" :treeItem="props.treeItem" :acive="state.acive" @returnOK="returnOK"></VM>
    <Container v-if="state.acive == '容器'" :treeItem="props.treeItem" :acive="state.acive"></Container>
    <ContainerImage v-if="state.acive == '容器镜像'" :treeItem="props.treeItem" :acive="state.acive"></ContainerImage>
    <Storage v-if="state.acive == '存储池'" :treeItem="props.treeItem" :acive="state.acive"></Storage>
    <Switch v-if="state.acive == '本地交换机'" :treeItem="props.treeItem" :acive="state.acive" @returnOK="returnOK"></Switch>
    <Hardware v-if="state.acive == '硬件设备'" :treeItem="props.treeItem" :acive="state.acive"></Hardware>
    <Shuttering v-if="state.acive == '模板'" :treeItem="props.treeItem" :acive="state.acive"></Shuttering>
    <RecycleBin v-if="state.acive == '回收站'" :treeItem="props.treeItem" :acive="state.acive" @returnOK="returnOK"></RecycleBin>
  </div>
  <HostEdit :editTime="state.editTime" :treeItem="props.treeItem" @returnOK="returnOK"></HostEdit>
  <HostDelete :deleteTime="state.deleteTime" :treeItem="props.treeItem" @returnOK="returnOK"></HostDelete>
  <AddVM :addVmTime="state.addVmTime" :treeItem="props.treeItem" @returnOK="returnOK"></AddVM>
</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { CirclePlus,Edit,Delete,MostlyCloudy,Fold,SwitchButton,Refresh,ArrowDown } from '@element-plus/icons-vue'; // ICON
const HostSummary = defineAsyncComponent(() => import('./HostSummary.vue'));
const VM = defineAsyncComponent(() => import('../resourcePublic/vm/VM.vue'));
const Container = defineAsyncComponent(() => import('../resourcePublic/container/index.vue'));
const ContainerImage = defineAsyncComponent(() => import('../resourcePublic/containerImage/index.vue'));
const Storage = defineAsyncComponent(() => import('../resourcePublic/storage/Storage.vue'));
const Switch = defineAsyncComponent(() => import('../resourcePublic/switch/Switch.vue'));
const Hardware = defineAsyncComponent(() => import('../resourcePublic/hardware/Hardware.vue'));
const Shuttering = defineAsyncComponent(() => import('../resourcePublic/shuttering/index.vue'));
const RecycleBin = defineAsyncComponent(() => import('../resourcePublic/recycleBin/index.vue'));

const HostEdit = defineAsyncComponent(() => import('./HostEdit.vue'));
const HostDelete = defineAsyncComponent(() => import('./HostDelete.vue'));
const AddVM = defineAsyncComponent(() => import('../resourcePublic/addVM/index.vue'));
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据

const handleCommand = (command: string | number | object) => {
  switch (command) {
    case 'xg':
      editClick()
      break;
    case 'tb':
      console.log('同步主机未开发')
      break;
    case 'gb':
      console.log('关闭主机未开发')
    case 'cq':
      console.log('重启主机未开发')
    case 'wh':
      console.log('退出维护模式未开发')
    case 'sc':
      deleteClick()
      break;
  }
}

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  }
});
const state = reactive({
  routeList: [
    { name: '概要', code: permisData.zhujigaiyao },
    { name: '虚拟机', code: permisData.xunijigaiyao },
    // { name: '容器', code: permisData.rongqi },
    // { name: '容器镜像', code: permisData.rongqijingxiang },
    { name: '存储池', code: permisData.cunchuchi },
    { name: '本地交换机', code: permisData.bendijiaohuanji },
    { name: '硬件设备', code: permisData.yingjianshebei },
    { name: '模板', code: permisData.moban },
    { name: '回收站', code: permisData.huishouzhan },
  ],
  // routeList: ['概要','虚拟机','容器','容器镜像','存储池','本地交换机','硬件设备','模板','回收站'],
  acive: '概要',
  editTime: '',
  deleteTime: '',
  addVmTime: '',
});
const tagClick=(v: string) => {
  state.acive = v
}
const isActive = (v: string) => {
	if(state.acive ===v) {
    return true
  } else {
    return false
  }
};
const editClick = () => {
  state.editTime = ""+new Date()
}
const deleteClick = () => {
  state.deleteTime = ""+new Date()
}
const addVMClick = () => {
  state.addVmTime = ""+new Date()
}
const emit = defineEmits(['returnOK']);
const returnOK = (item: string)=>{
  emit('returnOK', item);
}
watch(
  ()=> props.treeItem,
  (val)=>{
    // state.acive = '概要'
  }
);
const powerJudge = ()=>{
  console.log('物理机节点-list')
  const firstAvailableRoute = state.routeList.find(item => item.code === true);
  if (firstAvailableRoute) {
    state.acive = firstAvailableRoute.name;
  }
}
// 页面加载时
onMounted(() => {
  powerJudge()
});
</script>
<style lang="scss" scoped>
.tabs-item-area {
  width: calc(100%);
	height: calc(100%);
  .dropdown-bth {
    margin: 0 15px;
  }
  .route-list {
    width: calc(100% - 40px);
    height: 55px;
    // background: var(--el-fill-color-blank);
    background: #faf7f7;
    border-radius: 26px;
    margin: 10px;
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .route-item {
      position: relative;
      padding: 0 20px;
      font-size: 14px;
      line-height: 50px;
      cursor: pointer;
      margin: 0 10px;
      color: var(--el-color-title);
      border-radius: 3px;
      display: flex;
      height: 75%;
      align-items: center;

      &:hover {
        background: var(--el-color-primary-light-9);
        font-weight: bold;
        color: var(--el-color-primary);
        &::before {
          content: ' ';
          position: absolute;
          width: 4px;
          height: 18px;
          top: 50%;
          transform: translateY(-50%);
          background: var(--el-color-primary);
          left: 0;
        }
      }
    }

    .is-active {
      // background: var(--el-color-primary-light-9);
      background: #fff9f5;
      font-weight: bold;
      color: var(--el-color-primary);
      &::before {
        content: ' ';
        position: absolute;
        width: 4px;
        height: 18px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--el-color-primary);
        left: 0;
      }
    }
  }
  .tabs-item-center {
    padding: 10px;
    border-radius: 10px;
    width: calc(100%);
    height: calc(100% - 110px);
    background: var(--el-fill-color-blank);
  }

}
</style>
