<template>
<div class="tabs-item-area">
  <div class="tabs-item-btn">
    <el-button type="primary" @click="addClick" :icon="CirclePlus" v-if="permisData.tianjiazhujichi">添加主机池</el-button>
  </div>
	<div class="route-list">
			<div v-for="item in state.routeList" v-show="item.code" :class="{ 'route-item': true, 'is-active': isActive(item.name) }" :key="item.name" @click="tagClick(item.name)">
				<span><span>{{ item.name }}</span></span>
			</div>
	</div>
	<div class="tabs-item-center">
    <ResourceSummary v-if="state.acive == '概要'" :treeItem="props.treeItem" :acive="state.acive"></ResourceSummary>
    <HostPool v-if="state.acive == '主机池'" :treeItem="props.treeItem" :acive="state.acive"></HostPool>
    <Colony v-if="state.acive == '集群'" :treeItem="props.treeItem" :acive="state.acive"></Colony>
    <Host v-if="state.acive == '物理机'" :treeItem="props.treeItem" :acive="state.acive"></Host>
    <VM v-if="state.acive == '虚拟机'" :treeItem="props.treeItem" :acive="state.acive"></VM>
    <Shuttering v-if="state.acive == '模板'" :treeItem="props.treeItem" :acive="state.acive"></Shuttering>
    <RecycleBin v-if="state.acive == '回收站'" :treeItem="props.treeItem" :acive="state.acive"></RecycleBin>
	</div>
  <PoolNew :newTime="state.newTime" :treeItem="props.treeItem" @returnOK="returnOK"></PoolNew>
</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref } from 'vue';
import { CirclePlus } from '@element-plus/icons-vue'; // ICON
const ResourceSummary = defineAsyncComponent(() => import('./ResourceSummary.vue'));
const HostPool = defineAsyncComponent(() => import('./HostPool.vue'));
const Colony = defineAsyncComponent(() => import('../resourcePublic/colony/Colony.vue'));
const Host = defineAsyncComponent(() => import('../resourcePublic/host/Host.vue'));
const VM = defineAsyncComponent(() => import('../resourcePublic/vm/VM.vue'));
const Shuttering = defineAsyncComponent(() => import('../resourcePublic/shuttering/index.vue'));
const RecycleBin = defineAsyncComponent(() => import('../resourcePublic/recycleBin/index.vue'));
const PoolNew = defineAsyncComponent(() => import('./PoolNew.vue'));
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据
// 定义变量内容
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  }
});
const state = reactive({
  routeList: [
    { name: '概要', code: permisData.ziyuanjiediangaiyao },
    { name: '主机池', code: permisData.zhujichi },
    { name: '集群', code: permisData.jiqun },
    { name: '物理机', code: permisData.wuliji },
    { name: '虚拟机', code: permisData.xuniji },
    { name: '模板', code: permisData.moban },
    { name: '回收站', code: permisData.huishouzhan },
  ],
  acive: '概要',
  newTime: '',
});

const tagClick=(v: string) => {
  state.acive = v
}
const isActive = (v: string) => {
	if(state.acive ===v) {
    return true
  } else {
    return false
  }
};
const addClick = () => {
  state.newTime = ''+new Date()
}
const emit = defineEmits(['returnOK']);
const returnOK = (item: string)=>{
  emit('returnOK', item);
}
const powerJudge = ()=>{
  console.log('资源节点-list')
  const firstAvailableRoute = state.routeList.find(item => item.code === true);
  if (firstAvailableRoute) {
    state.acive = firstAvailableRoute.name;
  }
}
// 页面加载时
onMounted(() => {
  powerJudge()
});
</script>
<style lang="scss" scoped>
.tabs-item-area {
  width: calc(100%);
	height: calc(100%);
  .route-list {
    width: calc(100% - 40px);
    height: 55px;
    // background: var(--el-fill-color-blank);
    background: #faf7f7;
    border-radius: 26px;
    margin: 10px;
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .route-item {
      position: relative;
      padding: 0 20px;
      font-size: 14px;
      line-height: 50px;
      cursor: pointer;
      margin: 0 10px;
      color: var(--el-color-title);
      border-radius: 3px;
      display: flex;
      height: 75%;
      align-items: center;

      &:hover {
        background: var(--el-color-primary-light-9);
        font-weight: bold;
        color: var(--el-color-primary);
        &::before {
          content: ' ';
          position: absolute;
          width: 4px;
          height: 18px;
          top: 50%;
          transform: translateY(-50%);
          background: var(--el-color-primary);
          left: 0;
        }
      }
    }

    .is-active {
      // background: var(--el-color-primary-light-9);
      background: #fff9f5;
      font-weight: bold;
      color: var(--el-color-primary);
      &::before {
        content: ' ';
        position: absolute;
        width: 4px;
        height: 18px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--el-color-primary);
        left: 0;
      }
    }
  }
  .tabs-item-center {
    padding: 10px;
    border-radius: 10px;
    width: calc(100%);
    height: calc(100% - 110px);
    background: var(--el-fill-color-blank);
  }

}
</style>
