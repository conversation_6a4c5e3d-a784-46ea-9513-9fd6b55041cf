<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
					<div class="pool-tree-area">
						<div class="pool-btn">
							<el-tooltip effect="light" content="添加分组" placement="top" v-if="powerItem.tianjia">
								<el-link @click="newClick"><el-icon><CirclePlus /></el-icon></el-link>
							</el-tooltip>
							<el-tooltip effect="light" content="修改分组" placement="top" v-if="powerItem.xiugai">
								<el-link @click="editClick" :disabled="state.treePID=='0'"><el-icon><Edit /></el-icon></el-link>
							</el-tooltip>
							<el-tooltip effect="light" content="刷新分组" placement="top" v-if="powerItem.liebiao">
								<el-link @click="treePoolQuery('reset')"><el-icon><RefreshRight /></el-icon></el-link>
							</el-tooltip>
							<el-tooltip effect="light" content="删除分组" placement="top" v-if="powerItem.shanchu">
								<el-link type="danger" @click="deletedClick" :disabled="state.treePID=='0'"><el-icon><Delete /></el-icon></el-link>
							</el-tooltip>
						</div>
						<div class="tree-area" v-if="powerItem.liebiao">
							<el-tree
								ref="treeRef"
								node-key="id"
								:highlight-current="true"
								:default-expand-all="true"
								:expand-on-click-node="false"
								:data="state.treeData"
								:props="defaultProps"
								@node-click="handleNodeClick"
							>
								<template #default="{ node, data }">
									<span style="display: flex; align-items: center">
										<el-icon v-if="data.icon"><component :is="data.icon" /></el-icon>
										<span style="margin-left: 8px">{{ node.label }}</span>
									</span>
								</template>
							</el-tree>
							<!-- <ul id="treeDemo" class="ztree"></ul> -->
						</div>
						<GroupOperate ref="operateRef" @groupOK="groupOK"></GroupOperate>
						<TableDelete :names='[state.treeName]' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
					</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<TablePool ref="poolRef" />
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
import { CirclePlus, Edit, RefreshRight, Delete } from '@element-plus/icons-vue'; // ICON
import { Search } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { poolTreeQuery,poolTreeDelete } from '/@/api/StoreManage'; // 接口
import { ElMessage } from 'element-plus';
const GroupOperate = defineAsyncComponent(() => import('./GroupOperate.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const TablePool = defineAsyncComponent(() => import('./tablePool/index.vue'));

// 定义变量内容
const defaultProps = {
	children: 'children',
	label: 'name',
};
const state = reactive({
	treeData: [{id: '',pid:'', label: '', children: []} ],
	treeID: '',
	treePID: '',
	treeName: '',
	deleteTime: '',
});
const operateRef = ref();
// 添加组
const newClick = () => {
  operateRef.value.openDialog({type:'new',id:state.treeID})
}
// 修改组
const editClick = () => {
  operateRef.value.openDialog({type:'edit',id:state.treeID,name:state.treeName})
}
// 删除组
const deletedClick = () => {
  state.deleteTime = '存储池分组/'+new Date()
}
// 存储池选择返回
const poolRef = ref();

const treeRef = ref();
const treePoolQuery = async (type:string) => {
	state.treeData = []
	poolTreeQuery().then((res:any) => {
		state.treeData = res.data
		if(type == 'reset') {
			state.treeID = state.treeData[0].id;
			state.treePID = state.treeData[0].pid;
			state.treeName = state.treeData[0].label;
		}
		if (treeRef.value && state.treeData.length > 0) {
			treeRef.value.setCurrentKey(state.treeID); // 选中第一行
			poolRef.value.openDialog(state.treeData)
		}
	})
};
interface Tree {
	name: string;
	id: string;
	pid: string;
	icon?: any;
	children?: Tree[];
}
const handleNodeClick = (data: Tree) => {
	state.treeID = data.id
	state.treePID = data.pid
	state.treeName = data.name;
	poolRef.value.openDialog(data)
};
const groupOK = (item: string) => {
	treePoolQuery(item)
}
// 删除返回参数
const returnOK = (item: string) => {
	if(item == 'delete') {
		treePoolQuery('reset')
    poolTreeDelete({
      name: state.treeName,
      id: state.treeID,
    })
    .then(res => {
      if(res.msg == 'ok'){
        treePoolQuery('reset')
        ElMessage.success('删除分组操作完成');
      }else {
        ElMessage.error('删除分组操作失败');
      }
    })
  }
}
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	tianjia: false,
  shanchu: false,
  xiugai: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'cunchuchitianjiafenzu',
    'cunchuchishanchufenzu',
    'cunchuchixiugaifenzu',
    'fenbushicunchuchiliebiao',
    'IPsanxiugai',
    'IPsansaomiao',
    'IPsanshanchu',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.fenbushicunchuchiliebiao;
		powerItem.tianjia = res.data.cunchuchitianjiafenzu;
		powerItem.shanchu = res.data.cunchuchishanchufenzu;
		powerItem.xiugai = res.data.cunchuchixiugaifenzu;
		if(powerItem.liebiao) {
			treePoolQuery('reset');
		}
		// setTimeout(() => {
		// }, 500);
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 220px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-tree-area {
	width: 100%;
	height: 100%;
	.pool-btn {
		display: flex;
		justify-content: space-evenly;
		margin-bottom: 15px;
	}
	.tree-area {
		width: 100%;
		height: calc(100% - 50px);
		overflow: auto;
	}
}
			
		}
		.resource-pool-tabs {
			width: calc(100% - 240px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}

</style>
