<template>
  <div class="resource-pool-container">
    <div class="left-information-area">
      <div class="colony-information-area">
        <p class="information-title">
          <span>物理机信息</span>
          <el-button type="primary" class="title-synchronous" plain :icon="Refresh" :disabled="state.synchronous" @click="synchronousClick">同步数据</el-button>
        </p>
        <!-- <p class="information-title"><span>集群信息</span></p> -->
        <div class="colony-content">
          <img src="../../../assets/resource/wlj.jpg" alt="">
          <div><span>物理机SN:</span><span></span><span>{{ state.framework }}</span></div>
          <div><span>物理网卡:</span><span></span><span>{{ state.networkCard }}</span></div>
          <div><span>IP地址:</span><span></span><span>{{ state.address }}</span></div>
          <div><span>物理机IQN:</span><span></span><span>{{ state.iqn }}</span></div>
          <div><span>IOMMU状态:</span><span></span><span>{{ state.iommu }}</span></div>
          <div><span>BMC链接:</span><span></span><span>{{ state.bmc }}</span></div>
          <div><span>维护状态:</span><span></span><span>{{ state.maintenanceStatus }}</span></div>
          <div><span>连接状态:</span><span></span><span>{{ state.linkState }}</span></div>
          <div><span>NUMA节点数:</span><span></span><span>{{ state.numa }}</span></div>
          <div><span>CPU插槽数:</span><span></span><span>{{ state.cpuSlot }}</span></div>
          <div><span>每插槽CPU核数:</span><span></span><span>{{ state.cpuPit }}</span></div>
          <div><span>每个核心线程数:</span><span></span><span>{{ state.cpuThread }}</span></div>
        </div>
      </div>
    </div>
    <div class="middle-information-area">
      <div class="config-information-area">
        <p class="information-title"><span>配置信息</span></p>
        <div class="config-content">
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/cpu.jpg" alt="">
              <div>
                <p><span>CPU总频率:</span><span>{{ hzConversion(state.cpuFrequencyTotal) }}</span></p>
                <p><span>已使用频率:</span><span>{{ hzConversion(state.cpuFrequencyUsed) }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>CPU使用率</span>
              <el-progress :percentage="state.cpuRate? state.cpuRate : 0"/>
            </div>
          </div>
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/nc.jpg" alt="">
              <div>
                <p><span>内存:</span><span>{{ capacityConversion(state.mem) }}</span></p>
                <p><span>已使用内存:</span><span>{{ capacityConversion(state.memUsed) }}</span></p>
                <p><span>已分配内存:</span><span>{{ capacityConversion(state.memAssigned) }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>内存使用率</span>
              <el-progress :percentage="state.memRate? state.memRate : 0"/>
            </div>
          </div>
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/cc.jpg" alt="">
              <div>
                <p><span>存储:</span><span>{{ capacityConversion(state.stor) }}</span></p>
                <p><span>已使用存储:</span><span>{{ capacityConversion(state.storUsed) }}</span></p>
                <p><span>已分配存储:</span><span>{{ capacityConversion(state.storAssigned) }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>存储使用率</span>
              <el-progress :percentage="state.storRate? state.storRate : 0"/>
            </div>
          </div>
        </div>
      </div>
      <div class="resource-allocation-area">
         <p class="information-title"><span>资源分配</span></p>
      </div>
    </div>
    <div class="right-information-area">
      <div class="strategy-information-area">
        <p class="information-title"><span>策略信息</span></p>
      </div>
      <div class="resource-usage-area">
        <p class="information-title-long"><span>资源使用排名（前五）</span></p>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search,Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';
import { dayjs } from 'element-plus';
import { hostOverview,synchronousHardware } from '/@/api/ResourcePool'; // 接口
import { capacityConversion,hzConversion } from '/@/model/resource'; // 表格 正则
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  framework: '-',
  networkCard: '-',
  address: '-',
  iqn: '-',
  iommu: '-',
  bmc: '-',
  maintenanceStatus: '-',
  linkState: '-',
  numa: '-',
  cpuSlot: '-',
  cpuPit: '-',
  cpuThread: '-',
  cpuFrequencyTotal: '-',
  cpuFrequencyUsed: '-',
  cpuRate: 0,
  mem: '-',
  memUsed: '-',
  memAssigned: '-',
  memRate: 0,
  stor: '-',
  storUsed: '-',
  storAssigned: '-',
  storRate: 0,
  synchronous: false,
});
// 同步数据
const synchronousClick = () => {
  state.synchronous = true;
  setTimeout(() => {
    state.synchronous = false;
  }, 1500);
  synchronousHardware(props.treeItem.id).then(res=>{
    ElMessage.success('修改该主机池操作完成')
    summaryQuery()
  })
};
// 概要 数据
const summaryQuery=()=>{
	// let libs = ['测试1', '测试2', '测试3', '测试4'];
  // state.framework = libs[Math.round(Math.random() * 3)]
  hostOverview(props.treeItem.id).then(res=>{
    state.framework = res.data.host.serial_number
    state.networkCard = res.data.card?.map((item:any)=>{
      return item.name
    }).toString()
    state.address = res.data.host.ip
    state.iqn = res.data.host.iscsi_initiator
    state.iommu = res.data.host.iommu_status
    state.bmc = res.data.host.bmc==''?'-':res.data.host.bmc
    state.maintenanceStatus = res.data.host.is_maintain==0?'正常模式':'维护模式'
    state.linkState = res.data.host.is_connected==0?'未连接':'已连接'
    state.numa = res.data.host.numa_cell
    state.cpuSlot = res.data.host.cpu_sockets
    state.cpuPit = res.data.host.cpu_threads_per_core+' 核'
    state.cpuThread = res.data.host.cpu_cores_per_socket
    state.mem = res.data.mem_all_count
    state.cpuFrequencyUsed = res.data.cpu_use_hz
    state.cpuFrequencyTotal = res.data.cpu_all_hz
    state.cpuRate = parseFloat((res.data.cpu_use_hz/res.data.cpu_all_hz*100).toFixed(1))
    state.memUsed = res.data.mem_use_count
    state.memAssigned = res.data.mem_allocation_count
    state.memRate = parseFloat((res.data.mem_use_count/res.data.mem_all_count*100).toFixed(1))
    state.stor = res.data.disk_all_count
    state.storUsed = res.data.disk_use_count
    state.storAssigned = res.data.disk_allocation_count
    state.storRate = parseFloat((res.data.disk_use_count/res.data.disk_all_count*100).toFixed(1))
  })
}
watch(
  ()=> props.treeItem,
  (val)=>{
		summaryQuery();
  }
);
onMounted(() => {
  summaryQuery()
})
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    display: flex;
    justify-content: space-between;
    .left-information-area {
      width: 560px;
      height: calc(100%);
      padding: 5px;
      .colony-information-area {
        width: calc(100%);
        height: calc(100%);
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .colony-content {
          img {
            width: 120px;
          }
          width: 100%;
          height: calc(100% - 50px);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          div {
            span {
              display: inline-block;
              width: 180px;
            }
            span:first-child {
              text-align: right;
            }
            span:nth-child(2) {
              width: 20px;
            }
            span:nth-child(3) {
              width: 270px;
            }
          }
        }
      }
    }
    .middle-information-area {
      width: 530px;
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      .config-information-area {
        width: 100%;
        height: 450px;
        margin-bottom: 20px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .config-content {
          width: 100%;
          padding: 0 30px;
          height: calc(100% - 50px);
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
        }
      }
      .resource-allocation-area {
        width: 100%;
        height: 450px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
      }
    }
    .right-information-area {
      width: 390px;
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      .strategy-information-area {
        width: 100%;
        height: 300px;
        margin-bottom: 20px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
      }
      .resource-usage-area {
        width: 100;
        height: 700px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .information-title-long {
          height: 50px;
          display: flex;
          align-items: flex-end;
          span {
            display: inline-block;
            text-align: center;
            width: 180px;
            line-height: 30px;
            background-image: url('/@/assets/resource/title.jpg');
            background-size: 100% 100%;
          }
        }
      }
    }
    
  }
  .information-title {
    height: 50px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    .title-synchronous {
      margin-right: 20px;
    }
    span {
      display: inline-block;
      text-align: center;
      width: 100px;
      line-height: 30px;
		  background-image: url('/@/assets/resource/title.jpg');
      background-size: 100% 100%;
    }
  }
  .general-content {
    height: 130px;
    .general-img {
      display: flex;
      img {
        width: 120px;
      }
      div {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        p {
          span:nth-child(2) {
            margin-left: 15px;
          }
        }
      }
    }
    .general-progress {
      display: flex;
      justify-content: space-between;
      .el-progress {
        width: 320px;
      }
    }
  }
</style>